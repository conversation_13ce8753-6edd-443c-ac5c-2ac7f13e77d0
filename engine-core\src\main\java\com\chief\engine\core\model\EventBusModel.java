package com.chief.engine.core.model;

import com.chief.engine.core.event.SubscribeAttributeMsg;
import com.chief.model.api.ModelObject;
import com.chief.model.api.event.AttributeMsg;
import jakarta.annotation.Nullable;

import java.util.*;

import static com.chief.engine.core.constant.ID.EVENT_BUS_MODEL_ID;

public class EventBusModel extends AtomicModel {

    protected EventBusModel(@Nullable AtomicModel parent) {
        super(parent, EVENT_BUS_MODEL_ID);
    }

    /**
     * 属性名称-> (类-订阅者)（有序,根据类继承）
     */
    private Map<String, Map<Class<? extends ModelObject>, Set<Actor>>> attrSubscriptions = new HashMap<>();

    /**
     * 属性缓存
     * 属性名称 -> (id -> msg)
     */
    private Map<String, Map<String, AttributeMsg>> attrMsgCache = new HashMap<>();


    @Override
    protected boolean onMessage(Object message) {
        switch (message) {
            case SubscribeAttributeMsg msg -> this.doSubscribe(msg);
            case AttributeMsg msg -> this.publishAttribute(msg);
            default -> super.onMessage(message);
        }
        return true;
    }

    private void publishAttribute(AttributeMsg msg) {

        //缓存
        attrMsgCache.computeIfAbsent(msg.attrName(), k -> new HashMap<>())
                .put(msg.id(), msg);

        Set<Actor> subscribers = getSubscribers(msg);

        subscribers.forEach(actor -> actor.send(msg));
    }

    /**
     * 向父级递归 查找订阅者
     *
     * @param msg
     * @return
     */
    private Set<Actor> getSubscribers(AttributeMsg msg) {
        Map<Class<? extends ModelObject>, Set<Actor>> actorMap = attrSubscriptions.computeIfAbsent(msg.attrName(),
                k -> new HashMap<>());

        Class<? extends ModelObject> targetClazz = msg.objectType();

        Set<Actor> subscribers = new HashSet<>();

        while (!Object.class.equals(targetClazz)) {
            Set<Actor> actors = actorMap.get(targetClazz);
            if (actors != null) {
                subscribers.addAll(actors);
            }

            // 向上查找父类
            targetClazz = (Class<? extends ModelObject>) targetClazz.getSuperclass();
            if (targetClazz == null) {
                break;
            }
        }
        return subscribers;
    }

    /**
     * 执行订阅
     *
     * @param subMsg
     */
    private void doSubscribe(SubscribeAttributeMsg subMsg) {
        for (String attributeName : subMsg.attributeNames()) {
            Map<Class<? extends ModelObject>, Set<Actor>> map = attrSubscriptions.computeIfAbsent(attributeName, k -> new HashMap<>());
            map.computeIfAbsent(subMsg.objectType(), k -> new HashSet<>()).add(subMsg.subscriber());

            //首次订阅 从缓存中发布
            {
                Map<String, AttributeMsg> attrMessages = attrMsgCache.computeIfAbsent(attributeName, k-> new HashMap<>());
                attrMessages.values()
                        .stream()
                        .filter(msg -> subMsg.objectType().isAssignableFrom(msg.objectType()))
                        .forEach(msg -> {
                            subMsg.subscriber().send(msg);
                        });
            }

        }
    }

    public static void main(String[] args) {
        System.out.println(List.class.isAssignableFrom(ArrayList.class));
    }
}
