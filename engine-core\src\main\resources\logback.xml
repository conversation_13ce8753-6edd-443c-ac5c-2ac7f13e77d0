<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!--    <logger name="csim.model.handler" level="INFO" />-->
    <logger name="csim.model.handler" level="WARN"/>
    <!-- 设置全局日志级别为 INFO，并添加两个 appender -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
