package com.chief.engine.core.model;


import com.chief.engine.core.aware.EventBusModelAware;
import com.chief.engine.core.aware.RootModelAware;
import com.chief.engine.core.enums.EngineStatus;
import com.chief.engine.core.event.AddChildMsg;
import com.chief.engine.core.event.AddModelMsg;
import com.chief.engine.core.event.OnJoinMsg;
import com.chief.engine.core.event.TickMsg;
import com.chief.engine.core.util.ObjectHandlerUtils;
import com.chief.model.api.ModelObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import static com.chief.engine.core.constant.ID.ROOT_MODEL_ID;
import static com.chief.engine.core.enums.EngineStatus.*;

/**
 * 调度模型
 */
@Slf4j
public class RootModel extends AtomicModel {


    static long physicalStartTime;
    private final Map<String, AtomicModel> models = new ConcurrentHashMap<>();
    private EngineStatus status = created;
    private double speed = -1;
    private long physicalTime;
    private long simStartTime;
    private long simEndTime;
    private EventBusModel eventBus;

    private CompletableFuture<Void> stopFuture = new CompletableFuture<>();


    public RootModel(long simStartTime, long simEndTime) {
        super(null, ROOT_MODEL_ID);
        models.put(ROOT_MODEL_ID, this);

        this.simStartTime = simStartTime;
        this.simEndTime = simEndTime;
        this.send(new OnJoinMsg(simStartTime));

    }

    /**
     * 阻塞直到停止状态
     */
    public void blockUtilStop() {
        try {
            stopFuture.get();
        } catch (Exception e) {
            return;
        }
    }

    @Override
    protected void onJoinSimulation(long logicTime) {
        this.eventBus = new EventBusModel(this);
    }

    private void createObject(String pid, String id, ModelObject object) {
        AtomicModel parent = models.get(pid);
        Assert.notNull(parent, () -> "父节点[" + pid + "] 不存在");

        AtomicModel child;
        if (parent instanceof OperationModel<?>) {
            child = new OperationModel<>(parent, parent, id, ObjectHandlerUtils.getModelObjectHandler(object), object);
        } else {
            child = new OperationModel<>(parent, id, ObjectHandlerUtils.getModelObjectHandler(object), object);
        }

        if (child instanceof EventBusModelAware aware) {
            aware.setEventBusModel(this.eventBus);
        }

        if (child instanceof RootModelAware aware) {
            aware.setRootModel(this);
        }
        AtomicModel old = models.put(id, child);
        Assert.isNull(old, () -> "存在多个实体 id = \"" + id + "\"");
        AddChildMsg addChildMsg = new AddChildMsg(child);
        if (parent == this) {
            this.onMessage(addChildMsg);
        } else {
            parent.send(addChildMsg);
        }

    }

    public void start() {
        log.info("开始推演: 共{}个实体", models.size());
        status = running;
        physicalStartTime = System.currentTimeMillis();

        send(new TickMsg(simStartTime));
    }


    @Override
    protected void tick(TickMsg event) {

        this.physicalTime = System.currentTimeMillis();
        if (event.logicTime() > simEndTime) {
            status = stopped;
            long cost = System.currentTimeMillis() - physicalStartTime;
            log.info("cost: {}   倍速：{}", cost, (simEndTime - simStartTime) / 1.0 / cost);
            stopFuture.complete(null);
            return;
        }
        super.tick(event);
    }

    @SneakyThrows
    @Override
    protected void onTickComplete() {
        if (running.equals(this.status)) {
            long next = this.next();
            if (speed > 0) {
                long delta = next - getCurrentTime();
                long nextPhysicalTime = this.physicalTime + (long) (delta / speed);
                long nowTime = System.currentTimeMillis();
                if (nowTime < nextPhysicalTime) {
                    long hh = nextPhysicalTime - nowTime;
                    Thread.sleep(hh);
                    send(new TickMsg(next));
                } else {
                    send(new TickMsg(next));
                }
            } else {
                send(new TickMsg(next));
            }

        }
    }

    @Override
    protected void initMessageHandler() {
        addHandler(AddModelMsg.class, e -> {
            this.createObject(e.pid(), e.id(), e.object());
        });
        super.initMessageHandler();
    }


    /**
     * 可以外部调用
     *
     * @param id
     * @return
     */
    public ModelObject getModelObject(String id) {
        AtomicModel atomicModel = models.get(id);
        if (atomicModel instanceof OperationModel<?> operationModel) {
            return operationModel.getModelObject();
        } else {
            return null;
        }
    }
}
