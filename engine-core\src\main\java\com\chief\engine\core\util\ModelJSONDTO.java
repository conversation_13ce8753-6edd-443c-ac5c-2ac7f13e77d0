package com.chief.engine.core.util;

import com.chief.model.api.ModelObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
@JsonSerialize
public class ModelJSONDTO implements Serializable {

    @JsonProperty("Category")
    private String category;

    @JsonProperty("Properties")
    private Map<String, Object> properties;

    @JsonIgnore
    private ModelObject modelObject;

    @JsonProperty("ObjectInstanceHandle")
    private String objectInstanceHandle;

    @JsonProperty("Components")
    private List<ModelJSONDTO> components;


    @SneakyThrows
    public <T extends ModelObject> T toModelObject() {
        if (modelObject == null) {
            synchronized (this) {
                if (modelObject == null) {
                    Class objectClass = Class.forName("com.chief.model." + getPackageName() +".object." + getClazzName());
                    try {
                        modelObject = (ModelObject) ModelJsonUtils.parse(ModelJsonUtils.toJson(this.properties),
                                objectClass);
                    } catch (Exception e) {
                        log.warn("结构错误,无法反序列化");
                        if (properties != null) {
                            //结构可能对不上了 需要手动一个一个属性反序列化
                            modelObject = (ModelObject) objectClass.getConstructor().newInstance();
                            properties.entrySet().forEach(entry -> {
                                Field field = ReflectUtils.getField(objectClass, entry.getKey());
                                if (entry.getValue() != null) {
                                    try {
                                        Object attrVal = ModelJsonUtils.parse(ModelJsonUtils.toJson(entry.getValue()), field.getType());
                                        ReflectUtils.setField(field, modelObject, attrVal);
                                    } catch (Exception ex) {
//                                        log.error("{}.{} 属性结构非法.", objectClass.getSimpleName(), entry.getKey());
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }
        return (T) modelObject;
    }


    public void sync() {
        if (modelObject != null) {
            Map objectMap = ModelJsonUtils.parse(ModelJsonUtils.toJson(modelObject), Map.class);
            if (properties != null) {
                properties.putAll(objectMap);
            } else {
                properties = objectMap;
            }

        }
        if (components != null) {
            components.stream().forEach(ModelJSONDTO::sync);
        }
    }


    @JsonIgnore
    public String getClazzName() {
        Assert.hasText(category, "Category不能为空.");
        String[] split = category.split("-");
        return split[split.length - 1];
    }

    @JsonIgnore
    public String getPackageName() {
        String[] split = category.split("-");
        return split[0];
    }


    @JsonIgnore
    public Object getProperty(String name) {
        if (properties != null) {
            return properties.get(name);
        }
        return null;
    }

    @JsonIgnore
    public <T> T getProperty(String name, TypeReference<T> reference) {
        Object property = getProperty(name);
        if (property == null) {
            return null;
        }
        String json = ModelJsonUtils.toJson(property);
        return ModelJsonUtils.parse(json, reference);
    }


}
