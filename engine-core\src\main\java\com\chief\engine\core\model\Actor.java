package com.chief.engine.core.model;


import jakarta.annotation.Nullable;
import lombok.EqualsAndHashCode;
import org.jctools.queues.MpscLinkedQueue;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Actor线程调度层面
 *
 * @param <T>
 */
@EqualsAndHashCode(of = "id")
public abstract class Actor<T> {

    static final int status_idle = 0;
    static final int status_running = 1;


    //    public static final ForkJoinPool executor = new ForkJoinPool(Runtime.getRuntime().availableProcessors(),
//            defaultForkJoinWorkerThreadFactory, null, false,
//            0, 0x7fff, 1, null, 60_000L, TimeUnit.MILLISECONDS);
    public static ForkJoinPool executor = new ForkJoinPool();


    private final Actor<?> executorParent;

    private final String id;

    private volatile AtomicInteger status = new AtomicInteger(status_idle);

    private MpscLinkedQueue<ActorMessage> messages;
    private List<Matcher> matchers = new ArrayList<>();
    private ActorMessage currentMessage;

    public Actor(String id) {
        this(id, null);
    }

    /**
     * @param id
     * @param executorParent 不为空的话，则说明不是独立的Actor，跟父级在同一个调度单元，跟父级之间的调用是线程安全的
     */
    public Actor(String id, @Nullable Actor<?> executorParent) {
        Assert.notNull(id, "id 不能为空");
        this.id = id;
        if (executorParent != null) {
            this.executorParent = executorParent;
        } else {
            this.messages = new MpscLinkedQueue<>();
            this.executorParent = null;
        }

        this.initMessageHandler();
    }

    /**
     * 初始化消息处理器
     */
    protected void initMessageHandler() {
    }

    public String getId() {
        return id;
    }

    protected final boolean onMessage(T message) {
        for (Matcher matcher : matchers) {
            if (matcher.type().isInstance(message)) {
                if (matcher instanceof ConsumerMatcher consumerMatcher) {
                    consumerMatcher.consumer().accept(message);
                } else {
                    Object returnVal = ((FunctionMatcher) matcher).function.apply(message);
                    getSender().send(returnVal, this);
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 添加消息处理器
     *
     * @param clazz
     * @param consumer
     */
    protected final <M> void addHandler(Class<M> clazz, Consumer<M> consumer) {
        this.matchers.add(new ConsumerMatcher(clazz, consumer));
    }

    /**
     * 添加消息处理器
     *
     * @param clazz
     * @param function
     */
    protected final <M> void addHandler(Class<M> clazz, Function<M, Object> function) {
        this.matchers.add(new FunctionMatcher(clazz, function));
    }

    public void send(T message) {
        doSend(new ActorMessage(this, null, message));
    }

    public void send(T message, @Nullable Actor source) {
        doSend(new ActorMessage(this, source, message));
    }

    private void doSend(ActorMessage message) {
        if (executorParent == null) {
            messages.offer(message);
            registerForExecution();
        } else {
            executorParent.doSend(message);
        }
    }

    protected Actor getSender() {
        return currentMessage.source();
    }

    private void run() {
        try {
            while (true) {
                int drain = messages.drain(m -> {
                    this.currentMessage = m;
                    m.target().onMessage(m.msg());
                }, 5);
                if (drain == 0) {
                    break;
                }
            }
        } finally {
            status.compareAndSet(status_running, status_idle);
            registerForExecution();
        }
    }


    private void registerForExecution() {
        if (status.get() == status_idle && !messages.isEmpty()) {
            if (status.compareAndSet(status_idle, status_running)) {
                executor.execute(this::run);
            }
        }
    }

    sealed interface Matcher<T> permits ConsumerMatcher, FunctionMatcher {
        Class<T> type();
    }

    /**
     * @param target 消息目标
     * @param source 消息源头
     * @param msg    消息
     */
    record ActorMessage(Actor target, Actor source, Object msg) {
    }

    record ConsumerMatcher<T>(Class<T> type, Consumer<T> consumer) implements Matcher {
    }

    record FunctionMatcher<T, R>(Class<T> type, Function<T, R> function) implements Matcher {
    }

}
