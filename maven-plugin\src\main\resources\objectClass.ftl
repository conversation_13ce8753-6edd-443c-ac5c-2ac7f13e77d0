package ${pkg()};

<#if hasParent()>
import ${getParentClassFullName()};
</#if>
import java.io.Serializable;
import lombok.Data;
import com.chief.model.api.annotation.UpdateType;
import com.chief.model.api.enums.UpdateTypeEnum;

/**
 * ${formatSemantics(getSemantics(), " * ")}
 *
 * <AUTHOR>
 * @program: csim
 * @date ${getNow()}
 */
@Data
public class ${getClassName()}<#if hasParent()> extends ${getParentClassName()}<#else > implements com.chief.model.api.ModelObject</#if> {

<#if hasDeclaredAttributes()>
<#list getDeclaredAttributes() as attribute>
    <#if attribute.getSemantics()?? && attribute.getSemantics()?has_content>
    /**
     * ${formatSemantics(attribute.getSemantics(), "     * ")}
     */
    </#if><#if hasEffectiveUpdateType(attribute.name)>
    @UpdateType(${getUpdateType(attribute.name)})</#if>
    private ${getJavaTypeName(attribute.datatype.name)} ${attribute.name};

</#list>
</#if>
}
