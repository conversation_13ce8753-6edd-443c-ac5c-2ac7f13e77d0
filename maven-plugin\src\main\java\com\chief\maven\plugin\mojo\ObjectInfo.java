package com.chief.maven.plugin.mojo;

import com.chief.toolkit.fom.ACMetadata;
import com.chief.toolkit.fom.OCMetadata;
import com.chief.toolkit.fom.ObjectModel;
import com.chief.toolkit.fom.UpdateType;
import com.chief.toolkit.fom.datatype.IDatatype;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.chief.maven.plugin.mojo.GenerateModel.BASE_PACKAGE;
import static com.chief.maven.plugin.mojo.GenerateModel.fomService;
import static com.chief.maven.plugin.util.PackageUtils.getObjectPackage;

/**
 *
 * @param objectClass
 * @param model
 */
public record ObjectInfo(OCMetadata objectClass, ObjectModel model) {

    public String pkg() {
        return getObjectPackage(model.getModuleName());
    }


    public String getJavaTypeName(String dataTypeName) {
        DataTypeInfo dataTypeInfo = new DataTypeInfo(null, model);
        return dataTypeInfo.getJavaTypeName(dataTypeName);
    }

    public String getJavaTypeNameDTO(String dataTypeName) {
        DataTypeInfo dataTypeInfo = new DataTypeInfo(null, model);
        return dataTypeInfo.getJavaTypeNameDTO(dataTypeName);
    }

    public boolean isBasicJavaType(String dataTypeName) {
        return DataTypeInfo.isBasicJavaType(dataTypeName);
    }

    public String getNow() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return now.format(formatter);
    }

    public String formatSemantics(String semantics, String prefix) {
        if (semantics == null || semantics.trim().isEmpty()) {
            return "";
        }
        return semantics.lines()
                .map(line -> line.replaceFirst("^\\s+", "")) // 去掉行首空白
                .collect(Collectors.joining("\r\n" + prefix));
    }

    public String getParentClassName() {
        OCMetadata parent = objectClass.getParent();
        if (parent == null) {
            return null;
        }
        return parent.getName();
    }

    public String getParentClassFullName() {
        OCMetadata parent = objectClass.getParent();
        ObjectModel belongToModel = fomService.getBelongToModel(parent);
        return getObjectPackage(belongToModel.getModuleName()) + "." + parent.getName();
    }


    public List<ACMetadata> getDeclaredAttributes() {
        return objectClass.getDeclaredAttributes()
                .stream().sorted(Comparator.comparing(ACMetadata::getName))
                .toList();
    }

    public boolean hasParent() {
        OCMetadata parent = objectClass.getParent();
        return parent != null;
    }

    public boolean hasDeclaredAttributes() {
        Set<ACMetadata> attributes = objectClass.getDeclaredAttributes();
        return attributes != null && !attributes.isEmpty();
    }

    public String getClassName() {
        return objectClass.getName();
    }

    public String getSemantics() {
        return objectClass.getSemantics();
    }

    /**
     * 是否有 有效的Update类型 (Static, Periodic, Conditional)
     *
     * @param attrName
     * @return
     */
    public boolean hasEffectiveUpdateType(String attrName){
        ACMetadata attribute = objectClass.getAttribute(attrName);
        UpdateType updateType = attribute.getUpdateType();
        if (updateType == null) {
            return false;
        }
        switch (updateType) {
            case UPDATE_STATIC, UPDATE_PERIODIC, UPDATE_CONDITIONAL -> {
                return true;
            }
            default -> {
                return false;
            }
        }
    }

    public String getUpdateType(String attrName){
        ACMetadata attribute = objectClass.getAttribute(attrName);
        UpdateType updateType = attribute.getUpdateType();
        if (updateType == null) {
            return null;
        }
        switch (updateType) {
            case UPDATE_STATIC -> {
                return "UpdateTypeEnum.Static";
            }
            case UPDATE_CONDITIONAL -> {
                return "UpdateTypeEnum.Conditional";
            }

            case UPDATE_PERIODIC -> {
                return "UpdateTypeEnum.Periodic";
            }
            default -> {
                return null;
            }
        }
    }
}
