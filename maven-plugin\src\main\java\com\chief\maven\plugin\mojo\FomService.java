package com.chief.maven.plugin.mojo;

import com.chief.toolkit.fom.ICMetadata;
import com.chief.toolkit.fom.OCMetadata;
import com.chief.toolkit.fom.ObjectModel;
import com.chief.toolkit.fom.datatype.IDatatype;

public interface FomService {

    IDatatype getDatatype(String name);

    /**
     * 获取所属的 ObjectModel
     *
     * @param oc
     * @return
     */
    ObjectModel getBelongToModel(OCMetadata oc);

    /**
     * 获取所属的 ObjectModel
     *
     * @param ic
     * @return
     */
    ObjectModel getBelongToModel(ICMetadata ic);
}
