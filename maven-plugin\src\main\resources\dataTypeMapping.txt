HLAASCIIchar = Integer
HLAASCIIstring = String
MorpheusString = String
MorpheusBoolean = Boolean
HLAboolean = Integer
HLAbyte = Integer
HLAfloat32BE = Float
HLAfloat32LE = Float
HLAfloat64BE = Double
HLAfloat64LE = Double
HLAinteger16BE = Integer
HLAinteger16LE = Integer
HLAinteger32BE = Integer
HLAinteger32LE = Integer
HLAinteger64BE = Long
HLAinteger64LE = Long
HLAoctet = Integer
HLAoctetPairBE = Integer
HLAoctetPairLE = Integer
HLAunicodeChar = Integer
HLAunicodeString = String
RPRunsignedInteger16BE = Integer
RPRunsignedInteger32BE = Integer
RPRunsignedInteger64BE = Long
RPRunsignedInteger8BE = Integer
MarkingArray11 = String
MarkingArray31 = String
RTIobjectId = String
UnsignedInteger64Array1Plus = String
UUID = String