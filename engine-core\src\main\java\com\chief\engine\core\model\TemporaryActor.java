package com.chief.engine.core.model;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

/**
 * 临时Actor 短暂存在，不进入RootModel为根基的调度体系
 * 主要用于Ask请求
 */
public class TemporaryActor<Req,Res> extends Actor {

    private CompletableFuture<Res> future;

    public TemporaryActor() {
        super(UUID.randomUUID().toString());
    }

    public <Res> Future<Res> ask(Actor respondent, Req request){
        CompletableFuture<Res> future = new CompletableFuture<>();
        send(new DecorateAskMessage(respondent, request, future));
        return future;
    }

    @Override
    protected void initMessageHandler() {
        addHandler(DecorateAskMessage.class, this::onRequest);
    }


    private void onRequest(DecorateAskMessage askMessage){
        askMessage.respondent().send(askMessage.request(), this);
        this.future = askMessage.future();
    }

    private void onResponse(Res res){
        future.complete(res);
    }



}
 record DecorateAskMessage<Req,Res>(Actor respondent, Req request, CompletableFuture<Res> future){}