package com.chief.engine.core.model;

import com.chief.engine.core.aware.EventBusModelAware;
import com.chief.engine.core.aware.RootModelAware;
import com.chief.engine.core.event.SubscribeAttributeMsg;
import com.chief.engine.core.util.ReflectUtils;
import com.chief.model.api.IDataType;
import com.chief.model.api.ModelContext;
import com.chief.model.api.ModelObject;
import com.chief.model.api.ModelObjectHandler;
import com.chief.model.api.event.AttributeMsg;
import com.chief.model.api.event.InteractionMsg;
import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.lang.reflect.Field;

public class OperationModel<T extends ModelObject> extends AtomicModel
        implements ModelContext, EventBusModelAware, RootModelAware {
    /**
     * 业务模型处理器
     */
    private final ModelObjectHandler<T> handler;

    private final T modelObject;

    private EventBusModel eventBus;

    private RootModel rootModel;

    protected OperationModel(@Nullable AtomicModel parent, String id,
                             ModelObjectHandler handler, T modelObject) {
        this(parent, null, id, handler, modelObject);
    }

    protected OperationModel(@Nullable AtomicModel parent, @Nullable Actor executorParent, String id,
                             ModelObjectHandler handler, T modelObject) {
        super(parent, executorParent, id);
        this.handler = handler;
        this.modelObject = modelObject;
        this.handler.setContext(this);
    }


    @Override
    protected void update(long delta) {
        if (handler != null) {
            handler.update(delta);
        }
    }

    @Override
    public void setEventBusModel(EventBusModel eventBus) {
        this.eventBus = eventBus;
    }


    @Override
    public void setRootModel(RootModel rootModel) {
        this.rootModel = rootModel;
    }

    @Override
    public T getModelObject() {
        return modelObject;
    }

    @Override
    public void publishAttribute(String attrName) {
        Field field = ReflectUtils.getField(this.modelObject.getClass(), attrName);
        try {
            Serializable value = (Serializable) field.get(this.modelObject);
            if (value != null && value instanceof IDataType dataType) {
                value = dataType.toDTO();
            }
            eventBus.send(new AttributeMsg(
                    getCurrentTime(),
                    getId(),
                    this.modelObject.getClass(),
                    attrName,
                    value));
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void publishAttribute(String attrName, Serializable value) {
        eventBus.send(new AttributeMsg(
                getCurrentTime(),
                getId(),
                this.modelObject.getClass(),
                attrName,
                value));
    }

    @Override
    public ModelObject getModelObject(String id) {
        return rootModel.getModelObject(id);
    }


    @Override
    public void subscribeAttribute(Class objectType, String... attributeNames) {
        this.eventBus.send(new SubscribeAttributeMsg(this, objectType, attributeNames));
    }


    @Override
    public ModelContext getParentModelContext() {
        if (this.parent instanceof ModelContext parentModelContext) {
            return parentModelContext;
        }
        return null;
    }

    @Override
    protected void onJoinSimulation(long logicTime) {
        handler.onJoinSimulation(logicTime);
    }



    @Override
    protected boolean onMessage(Object message) {
        switch (message) {
            case AttributeMsg msg -> {
                if (handler != null) {
                    this.handler.onAttributeMsg(msg);
                }
            }

            case InteractionMsg msg -> {
                if (handler != null) {
                    handler.onInteractionMsg(msg);
                }
            }
            default -> super.onMessage(message);
        }
        return true;
    }


    protected long getFrameLength() {
        if (modelObject != null) {
            Integer frameLength = modelObject.getFrameLength();
            if (frameLength != null) {
                return frameLength;
            }
            return 1000;
        }
        return 1000;
    }
}
