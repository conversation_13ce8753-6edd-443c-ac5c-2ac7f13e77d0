package com.chief.maven.plugin.util;

import static com.chief.maven.plugin.mojo.GenerateModel.BASE_PACKAGE;

public class PackageUtils {


    public static String getObjectPackage(String moduleName) {
        return String.format("%s.%s.object", BASE_PACKAGE, moduleName);
    }

    public static  String getInteractionPackage(String moduleName) {
        return String.format("%s.%s.interaction", BASE_PACKAGE, moduleName);
    }


    public static  String getDataTypePackage(String moduleName) {
        return String.format("%s.%s.data_type", BASE_PACKAGE, moduleName);
    }

    public static  String getDataTypeDTOPackage(String moduleName) {
        return String.format("%s.%s.data_type.dto", BASE_PACKAGE, moduleName);
    }
}
