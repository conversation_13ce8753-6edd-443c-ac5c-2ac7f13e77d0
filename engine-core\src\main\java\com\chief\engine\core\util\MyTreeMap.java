package com.chief.engine.core.util;

import java.util.*;
import java.util.function.Function;

public class MyTreeMap<K extends Comparable, V> extends HashMap<K, V> {

    private PriorityQueue<MyEntry> queue = new PriorityQueue<>();

    @Override
    public V computeIfAbsent(K key, Function<? super K, ? extends V> mappingFunction) {
        V v = super.computeIfAbsent(key, k -> {
            V apply = mappingFunction.apply(k);
            queue.add(new MyEntry(k, apply));
            return apply;
        });
        return v;
    }

    public Map.Entry<K, V> firstEntry() {
        return queue.peek();
    }

    @Override
    public V put(K key, V value) {
        throw new UnsupportedOperationException();
    }

    public V remove(K key) {
        boolean remove = queue.remove(key);
        return super.remove(key);
    }

    class MyEntry implements Map.Entry<K, V>, Comparable<MyEntry> {

        private K k;
        private V v;

        public MyEntry(K k, V v) {
            this.k = k;
            this.v = v;
        }

        @Override
        public K getKey() {
            return k;
        }

        @Override
        public V getValue() {
            return v;
        }

        @Override
        public V setValue(V value) {
            throw new UnsupportedOperationException();
        }

        @Override
        public String toString() {
            return "key=" + k + ", value=" + v;
        }

        @Override
        public int compareTo(MyEntry o) {
            return this.k.compareTo(o.k);
        }

        @Override
        public boolean equals(Object obj) {
            if (obj instanceof MyEntry o) {
                return this.k.compareTo(o.k) == 0;
            }
            return false;
        }
    }


    public static void main(String[] args) {
        MyTreeMap<Long, Set<Integer>> map = new MyTreeMap<Long, Set<Integer>>();


        map.computeIfAbsent(2L, k -> new HashSet<>()).add(2);
        map.computeIfAbsent(4L, k -> new HashSet<>()).add(2);
        map.computeIfAbsent(1L, k -> new HashSet<>()).add(1);
        map.computeIfAbsent(1L, k -> new HashSet<>()).add(2);
        map.computeIfAbsent(1L, k -> new HashSet<>()).add(3);
        map.computeIfAbsent(3L, k -> new HashSet<>()).add(2);
        map.computeIfAbsent(1000L, k -> new HashSet<>()).add(2);
        map.computeIfAbsent(0L, k -> new HashSet<>()).add(2);

        System.out.println(map);

        System.out.println(map.firstEntry());
        map.remove(0L);
        System.out.println(map.firstEntry());
    }
}
