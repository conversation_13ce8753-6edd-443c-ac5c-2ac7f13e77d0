package ${pkg()};

import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import lombok.Data;
/**
 * ${formatSemantics(datatype().getSemantics(), " * ")}
 *
 * <AUTHOR>
 * @program: csim
 * @date ${getNow()}
 */
@Data
public class ${datatype().getName()} extends ArrayList<${getJavaTypeName(datatype().getDatatype().getName())}>
    implements com.chief.model.api.IDataType {

    public ${getJavaTypeNameDTO(datatype().getName())} toDTO(){
        ${getJavaTypeNameDTO(datatype().getDatatype().getName())}[] array = new ${getJavaTypeNameDTO(datatype().getDatatype().getName())}[this.size()];

        for (int i = 0; i < this.size(); i++) {
            array[i] = this.get(i)<#if !isBasicJavaType(datatype().getDatatype().getName())>.toDTO()</#if>;
        }
        return new ${getJavaTypeNameDTO(datatype().getName())}(array);
    }
}
