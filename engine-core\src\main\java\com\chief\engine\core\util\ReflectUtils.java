package com.chief.engine.core.util;

import lombok.SneakyThrows;
import org.springframework.lang.Nullable;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ReflectUtils {

    private static Map<Class, Map<String, Field>> reflectCacheField = new ConcurrentHashMap<>();

    /**
     * @param c
     * @param name field Name
     * @return
     */
    public static @Nullable Field getField(Class c, String name) {
        Map<String, Field> fieldMap = reflectCacheField.computeIfAbsent(c, k -> new ConcurrentHashMap<>());
        return fieldMap.computeIfAbsent(name, n -> {
            Field field = doGetField(c, name);
            field.setAccessible(true);
            return field;
        });
    }


    private static Field doGetField(Class c, String name) {
        try {
            return c.getDeclaredField(name);
        } catch (NoSuchFieldException e) {
            Class superclass = c.getSuperclass();
            if (superclass == null) {
                return null;
            }
            return doGetField(superclass, name);
        }
    }


    /**
     * 根据传入的属性名字符串，修改对应的属性值
     *
     * @param field 属性名
     * @param obj   要修改的实例对象
     * @param value 修改后的新值
     */
    @SneakyThrows
    public static void setField(Field field, Object obj, Object value) {
        field.setAccessible(true);
        field.set(obj, value);
    }

}
