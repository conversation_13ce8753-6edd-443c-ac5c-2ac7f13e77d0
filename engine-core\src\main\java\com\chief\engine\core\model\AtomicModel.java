package com.chief.engine.core.model;

import com.chief.engine.core.event.AddChildMsg;
import com.chief.engine.core.event.OnJoinMsg;
import com.chief.engine.core.event.TickCompleteMsg;
import com.chief.engine.core.event.TickMsg;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 引擎模型调度层面
 */
@Slf4j
public class AtomicModel extends Actor<Object> {

    /**
     * 尚未被调度的对象
     */
    protected final Set<AtomicModel> unScheduled = new HashSet<>();
    protected final AtomicModel parent;
    protected final Set<AtomicModel> children = new HashSet<>();
    protected int childrenTickCount = 0;
    private TreeMap<Long, Set<AtomicModel>> scheduleEvent = new TreeMap<>();
    /**
     * 上次调度的时间
     */
    private long lastTickTime;
    /**
     * 当前时间
     */
    private long currentTime;

    protected AtomicModel(
            @Nullable AtomicModel parent,
            String id) {
        this(parent, null, id);
    }


    /**
     * @param parent         父级调度模型
     * @param executorParent 如果不为空，则表示和父级共用一个调度线程，
     *                       在一个调度线程内，相互直接可以直接方法调用，是线程安全的
     * @param id
     */
    protected AtomicModel(
            @Nullable AtomicModel parent,
            @Nullable Actor executorParent,
            String id) {
        super(id, executorParent);
        Assert.hasText(id, "id must not be null");
        this.parent = parent;
        this.lastTickTime = currentTime;
        this.init();
    }


    private void doAddChild(AddChildMsg msg) {
        AtomicModel child = msg.child();
        unScheduled.add(child);
        this.children.add(child);
        child.send(new OnJoinMsg(getCurrentTime()));
    }

    protected void onJoinSimulation(long logicTime) {
        this.currentTime = logicTime;
    }

    /**
     * 当前推演时间
     *
     * @return
     */
    public long getCurrentTime() {
        return currentTime;
    }

    /**
     * 上次调度时间
     *
     * @return
     */
    protected long getLastTickTime() {
        return lastTickTime;
    }


    protected void tick(TickMsg msg) {
        this.lastTickTime = this.currentTime;
        this.currentTime = msg.logicTime();
        long delta = this.currentTime - this.lastTickTime;
        this.childrenTickCount = 0;
        try {
            try {
                update(delta);
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
            }


            if (!unScheduled.isEmpty()) {
                //尚未调度的实体 默认一秒后调度
                Set<AtomicModel> models = scheduleEvent.computeIfAbsent(currentTime, k -> new HashSet<>(this.children.size()));
                models.addAll(unScheduled);
                unScheduled.clear();
            }
            Map.Entry<Long, Set<AtomicModel>> entry = scheduleEvent.firstEntry();

            boolean completeNow = true;
            if (entry != null && entry.getKey().equals(currentTime)) {
                Set<AtomicModel> willTicked = scheduleEvent.remove(currentTime);
                childrenTickCount = willTicked.size();
                if (childrenTickCount > 0) {
                    completeNow = false;
                    for (AtomicModel model : willTicked) {
                        model.send(msg);
                    }
                }

            }

            if (completeNow) {
                this.onTickComplete();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 模型更新
     *
     * @param delta 增量时间
     */
    protected void update(long delta) {
    }

    private void doChildTickComplete(TickCompleteMsg event) {
        Assert.state(event.nextTime() > getCurrentTime(),
                () -> "nextTime(" + event.nextTime() + ") must be greater than currentTime(" + getCurrentTime() + ")  childrenTickCount: " + childrenTickCount);
        childrenTickCount--;
        scheduleEvent.computeIfAbsent(event.nextTime(), k -> new HashSet<>()).add(event.model());
        if (childrenTickCount == 0) {
            this.onTickComplete();
        }
    }

    /**
     * 自身tick完成
     * <p>
     * 内部调用
     */
    protected void onTickComplete() {
        long next = this.next();
        parent.send(new TickCompleteMsg(this, next));
    }


    /**
     * 下一帧
     *
     * @return
     */
    protected long next() {
        if (scheduleEvent.isEmpty()) {
            return getCurrentTime() + getFrameLength();
        }
        Map.Entry<Long, Set<AtomicModel>> entry = scheduleEvent.firstEntry();
        Long key = entry.getKey();
        return key;
    }

    protected long getFrameLength() {
        return 1000;
    }

    private List<Matcher> matchers = new ArrayList<>();

    private Map<Class, Object> matcherMap = new HashMap();

    private void init() {
//        matchers.add(new ConsumerMatcher<>(TickCompleteMsg.class, this::doChildTickComplete));
//        matchers.add(new ConsumerMatcher<>(TickMsg.class, this::tick));
//        matchers.add(new ConsumerMatcher<>(AddChildMsg.class, this::doAddChild));
//        matchers.add(new ConsumerMatcher<>(OnJoinMsg.class, msg-> this.onJoinSimulation(msg.logicTime())));

        add(TickCompleteMsg.class, this::doChildTickComplete);
        add(TickMsg.class, this::tick);
        add(AddChildMsg.class, this::doAddChild);
        add(OnJoinMsg.class, msg -> {
            this.onJoinSimulation(msg.logicTime());
        });
    }

    private <T> void add(Class<T> clazz, Consumer<T> consumer) {
        matcherMap.put(clazz, consumer);
    }

    private <T> void add(Class<T> clazz, Function<T, Object> func) {
        matcherMap.put(clazz, func);
    }

    private Object getFun(Class messageClazz) {
        return matcherMap.computeIfAbsent(messageClazz, c -> {
            Class superclass = c.getSuperclass();
            if (superclass == null) {
                return null;
            }
            return getFun(superclass);
        });
    }

    @Override
    protected boolean onMessage(Object message) {
        boolean f = false;


        Object fun = getFun(message.getClass());
        if (fun == null) {
            return false;
        }
        if (fun instanceof Function function) {
            Object returnVal = function.apply(message);
            getSender().send(returnVal, this);
        } else {
            ((Consumer)fun).accept(message);
        }
//        for (Matcher matcher : matchers) {
//            if (matcher.type().isInstance(message)) {
//                if (matcher instanceof ConsumerMatcher consumerMatcher) {
//                    consumerMatcher.consumer.accept(message);
//                } else {
//                    Object returnVal = ((FunctionMatcher) matcher).function.apply(message);
//                    getSender().send(returnVal, this);
//                }
//                f = true;
//                break;
//            }
//        }
//
//        switch (message) {
//            case TickCompleteMsg event -> doChildTickComplete(event);
//            case TickMsg event -> tick(event);
//            case AddChildMsg event -> doAddChild(event);
//            case OnJoinMsg event -> onJoinSimulation(event.logicTime());
//            default -> f = false;
//        }
        return f;
    }

    sealed interface Matcher<T> permits ConsumerMatcher, FunctionMatcher {
        Class<T> type();
    }

    record ConsumerMatcher<T>(Class<T> type, Consumer<T> consumer) implements Matcher {
    }

    record FunctionMatcher<T, R>(Class<T> type, Function<T, R> function) implements Matcher {
    }

    {
    }
}