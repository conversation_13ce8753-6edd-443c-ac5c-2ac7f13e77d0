package com.chief.maven.plugin.mojo;


import com.chief.maven.plugin.util.Assert;
import com.chief.toolkit.FOM;
import com.chief.toolkit.fom.*;
import com.chief.toolkit.fom.datatype.ArrayType;
import com.chief.toolkit.fom.datatype.FixedRecordType;
import com.chief.toolkit.fom.datatype.IDatatype;
import com.chief.toolkit.fom.datatype.VariantRecordType;
import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;
import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugin.MojoFailureException;
import org.apache.maven.plugins.annotations.LifecyclePhase;
import org.apache.maven.plugins.annotations.Mojo;
import org.apache.maven.plugins.annotations.Parameter;
import org.apache.maven.project.MavenProject;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;

import static com.chief.maven.plugin.mojo.DataTypeInfo.isBasicJavaType;
import static com.chief.maven.plugin.util.PackageUtils.*;

@Mojo(name = "generate-model", defaultPhase = LifecyclePhase.GENERATE_SOURCES)
public class GenerateModel extends AbstractMojo implements Serializable, FomService {

    public static final String BASE_PACKAGE = "com.chief.model";

    public static FomService fomService;

    @Parameter(property = "fomName", required = true, readonly = true)
    private String fomName;
    @Parameter(property = "fomHub", required = true, readonly = true)
    private String fomHub;

    @Parameter(defaultValue = "${project}")
    private MavenProject project;

    private Configuration configuration;

    private ObjectModel model;
    private Map<String, ObjectModel> modelMap;

    /**
     * root ->
     */
    private List<ObjectModel> modelStack;


    @Override
    public void execute() throws MojoExecutionException, MojoFailureException {
        fomService = this;
        {
            configuration = new Configuration(Configuration.VERSION_2_3_22);
            configuration.setClassForTemplateLoading(this.getClass(), "/");
            configuration.setTemplateLoader(new ClassTemplateLoader(this.getClass(), "/"));
            configuration.setDefaultEncoding("UTF-8");
            configuration.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        }


        this.model = getFom(fomName);

        Deque<ObjectModel> dependencyStack = new ArrayDeque<>();
        this.modelMap = loadDependency(model, null, dependencyStack);

        this.modelStack = new ArrayList<>(dependencyStack.size());
        while (!dependencyStack.isEmpty()) {
            ObjectModel item = dependencyStack.pop();
            modelStack.add(item);
        }

        try {
            generateDataType();
            generateObjectClasses();
            generateInteractions();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        project.addCompileSourceRoot(getOutputPath());


    }

    private void generateDataType() throws IOException {
        String pkg = getDataTypePackage(model.getModuleName());
        String pkgDTO = getDataTypeDTOPackage(model.getModuleName());
        Set<IDatatype> datatypes = model.getDatatypes();

        for (IDatatype datatype : datatypes) {
            if (isBasicJavaType(datatype)) {
                continue;
            }
            DataTypeInfo dataTypeInfo = new DataTypeInfo(datatype, model);
            File file = getFile(pkg, datatype.getName());
            File fileDTO = getFile(pkgDTO, datatype.getName() + "DTO");

            mkdir(file.getParentFile());
            mkdir(fileDTO.getParentFile());

            try (FileWriter writer = new FileWriter(file);
                 FileWriter writerDTO = new FileWriter(fileDTO)) {
                switch (datatype) {
                    case FixedRecordType type -> {
                        Template template = configuration.getTemplate("fixedDataTypes.ftl");
                        template.process(dataTypeInfo, writer);
                        template = configuration.getTemplate("fixedDataTypesDTO.ftl");
                        template.process(dataTypeInfo, writerDTO);
                    }
                    case ArrayType type -> {
                        Template template = configuration.getTemplate("arrayDataTypes.ftl");
                        template.process(dataTypeInfo, writer);
                        template = configuration.getTemplate("arrayDataTypesDTO.ftl");
                        template.process(dataTypeInfo, writerDTO);
                    }
                    case VariantRecordType type -> {
                        Template template = configuration.getTemplate("variantRecordTypes.ftl");
                        template.process(dataTypeInfo, writer);
                        template = configuration.getTemplate("variantRecordTypesDTO.ftl");
                        template.process(dataTypeInfo, writerDTO);
                    }
                    default -> {
                    }
                }
            } catch (TemplateException e) {
                e.printStackTrace();
                throw new RuntimeException(e);
            }


        }
    }

    private void generateObjectClasses() throws IOException {
        String pkg = getObjectPackage(model.getModuleName());

        // 获取所有对象类，排除HLAobjectRoot
        Set<OCMetadata> objectClasses = model.getAllObjectClasses();

        for (OCMetadata objectClass : objectClasses) {
            // 跳过根对象类
            if (belongTo(objectClass, model)) {
                ObjectInfo objectInfo = new ObjectInfo(objectClass, model);
                File file = getFile(pkg, objectClass.getName());

                mkdir(file.getParentFile());


                try (FileWriter writer = new FileWriter(file)) {

                    Template template = configuration.getTemplate("objectClass.ftl");
                    template.process(objectInfo, writer);

                } catch (TemplateException e) {
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        }
    }

    private void generateInteractions() throws IOException {
        String pkg = getInteractionPackage(model.getModuleName());

        // 获取所有交互类，排除HLAinteractionRoot
        Set<ICMetadata> interactionClasses = model.getAllInteractionClasses();

        for (ICMetadata interactionClass : interactionClasses) {
            // 跳过根交互类
            if (belongTo(interactionClass, model)) {
                InteractionInfo interactionInfo = new InteractionInfo(interactionClass, model);
                File file = getFile(pkg, interactionClass.getLocalName());

                mkdir(file.getParentFile());

                try (FileWriter writer = new FileWriter(file)) {
                    Template template = configuration.getTemplate("interactionClass.ftl");
                    template.process(interactionInfo, writer);
                } catch (TemplateException e) {
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        }
    }

    /**
     * interaction 是否属于 model
     *
     * @param ic
     * @param model
     * @return
     */
    private boolean belongTo(ICMetadata ic, ObjectModel model) {
        ObjectModel belongToModel = getBelongToModel(ic);
        if (model == belongToModel) {
            return true;
        }
        return model.getModuleName().equals(belongToModel.getModuleName());
    }

    /**
     * object 是否属于 model
     *
     * @param oc
     * @param model
     * @return
     */
    private boolean belongTo(OCMetadata oc, ObjectModel model) {
        ObjectModel belongToModel = getBelongToModel(oc);
        if (model == belongToModel) {
            return true;
        }
        return model.getModuleName().equals(belongToModel.getModuleName());
    }

    private void mkdir(File folder) {
        if (folder.exists()) {
            return;
        }
        folder.mkdirs();
    }

    private File getFile(String pkg, String simpleName) {
        String relativePath = pkg.replace(".", File.separator);
        File folder = new File(getOutputPath(), relativePath);
        return new File(folder, simpleName + ".java");
    }

    private String getOutputPath() {
        return String.format("%s/target/generated-sources/model/", project.getBasedir());
    }

    private Map<String, ObjectModel> loadDependency(ObjectModel model,
                                                    Map<String, ObjectModel> collector,
                                                    Deque<ObjectModel> dependencyStack) {
        if (collector == null) {
            collector = new HashMap<>();
            collector.put(model.getModuleName(), model);
        }
        dependencyStack.push(model);

        List<ModuleInfo> referenceModules = model.getReferenceModules();

        if (referenceModules != null) {
            for (ModuleInfo module : referenceModules) {
                ObjectModel item = getFom(module.getModuleName());
                collector.put(module.getModuleName(), item);
                loadDependency(item, collector, dependencyStack);
            }
        }

        return collector;
    }


    private ObjectModel getFom(String fomFileName) {
        try {
            if (!fomFileName.endsWith(".xml")) {
                fomFileName += ".xml";
            }
            File resourceDir = new File(fomHub);

            File fomFile = new File(resourceDir, fomFileName);

            ObjectModel objectModel = FOM.parseFOM(fomFile.toURI().toURL());

            Assert.equals(fomFileName, objectModel.getModuleName() + ".xml",
                    fomFileName + " 的模块名称和文件名不匹配");
            return objectModel;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public IDatatype getDatatype(String name) {
        IDatatype result = getDatatype(model, name);
        Assert.notNull(result, () -> "数据类型[" + name + "]不存在.");
        return result;
    }

    @Override
    public ObjectModel getBelongToModel(OCMetadata oc) {
        for (ObjectModel objectModel : modelStack) {
            if (objectModel.getObjectClass(oc.getName()) != null) {
                return objectModel;
            }
        }
        throw new IllegalArgumentException(oc.getName() + " 找不到归属模块.");
    }

    @Override
    public ObjectModel getBelongToModel(ICMetadata ic) {
        for (ObjectModel objectModel : modelStack) {
            if (objectModel.getInteractionClass(ic.getLocalName()) != null) {
                return objectModel;
            }
        }
        throw new IllegalArgumentException(ic.getLocalName() + " 找不到归属模块.");
    }


    public IDatatype getDatatype(ObjectModel model, String name) {
        IDatatype result = model.getDatatype(name);
        if (result != null) {
            return result;
        }

        List<ModuleInfo> references = model.getReferenceModules();

        if (references != null) {
            for (ModuleInfo module : references) {
                ObjectModel item = modelMap.get(module.getModuleName());
                if (item != null) {
                    result = getDatatype(item, name);
                    if (result != null) {
                        return result;
                    }
                }
            }
        }
        return null;
    }
}
