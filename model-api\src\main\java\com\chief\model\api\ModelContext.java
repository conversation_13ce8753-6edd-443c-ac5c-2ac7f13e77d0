package com.chief.model.api;

import com.chief.toolkit.support.SFunction;

import java.io.Serializable;
import java.util.List;

/**
 * 对外上下文
 */
public interface ModelContext<T extends ModelObject> {

    /**
     * 获取模型对象
     *
     * @return
     */
    T getModelObject();


    /**
     * 获取父级上下文
     * 一般用于 组件内获取平台
     *
     * @param <P>
     * @return
     */
    <P extends ModelObject> ModelContext<P> getParentModelContext();


    /**
     * 订阅属性
     * @param objectType
     * @param attributeNames
     */
    void subscribeAttribute(Class<? extends ModelObject> objectType, String... attributeNames);


    /**
     * 发布属性
     * @param attrName
     */
    void publishAttribute(String attrName);

    void publishAttribute(String attrName, Serializable value);


    /**
     * 获取当前仿真时间
     * @return
     */
    long getCurrentTime();


    /**
     * 根据ID获取模型对象
     * @param id
     * @return
     */
    <M extends ModelObject> M getModelObject(String id);
}
