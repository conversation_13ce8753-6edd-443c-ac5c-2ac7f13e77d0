package com.chief.engine.core.model;

import com.chief.model.api.ModelObjectHandler;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultModelObjectHandler extends ModelObjectHandler {
    @Override
    public void update(long delta) {

//        log.info("{} {}: 当前 {}", modelObject.getClass().getSimpleName(), name, DateUtil.format(new DateTime(context.getCurrentTime()), "yyyy-MM-dd HH:mm:ss"));
    }

}
