package com.chief.engine.core.util;

public class LightExecutorTest {

    public static void main(String[] args) throws InterruptedException {
        LightExecutor executor = new LightExecutor();

        for (int i = 0; i < 10; i++) {
            int finalI = i;
            executor.execute(() -> {
                System.out.println(finalI + "  " + Thread.currentThread());
            });
        }

        executor = new LightExecutor();

        for (int i = 10; i < 20; i++) {
            int finalI = i;
            executor.execute(() -> {
                System.out.println(finalI + "  " + Thread.currentThread().isVirtual());
            });
        }

        Thread.sleep(10000);
    }
}
