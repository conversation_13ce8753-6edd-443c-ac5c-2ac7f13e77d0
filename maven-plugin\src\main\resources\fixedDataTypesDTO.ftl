package ${pkgDTO()};

import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import lombok.Data;
/**
 * ${formatSemantics(datatype().getSemantics(), " * ")}
 * <AUTHOR>
 * @program: csim
 * @date ${getNow()}
*/
public record ${datatype().getName()}DTO (
<#list datatype().fields as field>
    <#if field.getSemantics()?? && field.getSemantics()?has_content>    /**
        * ${formatSemantics(field.getSemantics(), "    * ")}
        */</#if>
    ${getJavaTypeNameDTO(field.getDatatype().getName())} ${field.name}<#if field_has_next>,</#if>
</#list>
) implements com.chief.model.api.IDataTypeDTO {}
