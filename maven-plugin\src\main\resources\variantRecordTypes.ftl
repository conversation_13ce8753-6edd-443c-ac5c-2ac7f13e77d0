package ${pkg()};

import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import lombok.Data;
/**
 * ${formatSemantics(datatype().getSemantics(), " * ")}
 *
 * <AUTHOR>
 * @program: csim
 * @date ${getNow()}
 */
@Data
public class ${datatype().getName()} implements com.chief.model.api.IDataType {

<#list datatype().alternatives as field>
    /**
     * ${formatSemantics(field.getSemantics(), "     * ")}
     */
    private ${getJavaTypeName(field.datatype.name)} ${field.name};

</#list>
    public ${getJavaTypeNameDTO(datatype().getName())} toDTO(){
        return new ${getJavaTypeNameDTO(datatype().getName())}(
        <#list datatype().alternatives as field>
            <#if !isBasicJavaType(field.datatype.name)>this.${field.name} == null ? null : this.${field.name}.toDTO()<#else >this.${field.name}</#if><#if field_has_next>,</#if>
        </#list>
        );
    }
}
