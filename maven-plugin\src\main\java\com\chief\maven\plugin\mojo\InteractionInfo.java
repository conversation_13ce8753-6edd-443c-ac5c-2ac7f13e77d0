package com.chief.maven.plugin.mojo;

import com.chief.toolkit.fom.ICMetadata;
import com.chief.toolkit.fom.ObjectModel;
import com.chief.toolkit.fom.PCMetadata;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.chief.maven.plugin.mojo.GenerateModel.BASE_PACKAGE;
import static com.chief.maven.plugin.mojo.GenerateModel.fomService;

/**
 *
 * @param interactionClass
 * @param model
 */
public record InteractionInfo(ICMetadata interactionClass, ObjectModel model) {

    public String pkg() {
        return getPackagePrefix(model.getModuleName());
    }

    public String pkgDTO() {
        return getPackagePrefix(model.getModuleName()) + ".dto";
    }

    public String getJavaTypeName(String dataTypeName) {
        DataTypeInfo dataTypeInfo = new DataTypeInfo(null, model);
        return dataTypeInfo.getJavaTypeName(dataTypeName);
    }

    public String getJavaTypeNameDTO(String dataTypeName) {
        DataTypeInfo dataTypeInfo = new DataTypeInfo(null, model);
        return dataTypeInfo.getJavaTypeNameDTO(dataTypeName);
    }

    public boolean isBasicJavaType(String dataTypeName) {
        return DataTypeInfo.isBasicJavaType(dataTypeName);
    }

    public String getNow() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return now.format(formatter);
    }

    public String formatSemantics(String semantics, String prefix) {
        if (semantics == null || semantics.trim().isEmpty()) {
            return "";
        }
        return semantics.lines()
                .map(line -> line.replaceFirst("^\\s+", "")) // 去掉行首空白
                .collect(Collectors.joining("\r\n" + prefix));
    }

    public String getParentClassName() {
        ICMetadata parent = interactionClass.getParent();
        if (parent == null) {
            return null;
        }
        return parent.getLocalName();
    }

    public String getParentClassFullName() {
        ICMetadata parent = interactionClass.getParent();
        ObjectModel belongToModel = fomService.getBelongToModel(parent);
        return getPackagePrefix(belongToModel.getModuleName()) + "." + parent.getLocalName();
    }

    public List<PCMetadata> getDeclaredParameters() {
        return interactionClass.getDeclaredParameters()
                .stream().sorted(Comparator.comparing(PCMetadata::getName))
                .toList();
    }

    public boolean hasParent() {
        ICMetadata parent = interactionClass.getParent();
        return parent != null;
    }

    public boolean hasDeclaredParameters() {
        Set<PCMetadata> parameters = interactionClass.getDeclaredParameters();
        return parameters != null && !parameters.isEmpty();
    }

    public String getClassName() {
        return interactionClass.getLocalName();
    }

    public String getSemantics() {
        return interactionClass.getSemantics();
    }


    private String getPackagePrefix(String moduleName) {
        return String.format("%s.%s.interaction", BASE_PACKAGE, moduleName);
    }
}
