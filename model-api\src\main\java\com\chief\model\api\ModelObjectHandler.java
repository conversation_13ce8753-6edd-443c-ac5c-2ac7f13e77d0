package com.chief.model.api;

import com.chief.model.api.annotation.UpdateType;
import com.chief.model.api.enums.UpdateTypeEnum;
import com.chief.model.api.event.AttributeMsg;
import com.chief.model.api.event.InteractionMsg;

/**
 * 模型处理器
 */
public abstract class ModelObjectHandler<T extends ModelObject> {

    protected ModelContext<T> context;

    public final void setContext(ModelContext<T> context) {
        this.context = context;
    }


    public void onJoinSimulation(long logicTime) {
    }

    /**
     * 调度
     */
    public abstract void update(long delta);

    /**
     * 外部属性订阅
     *
     * @param attributeMsg
     */
    public void onAttributeMsg(AttributeMsg attributeMsg){}


    /**
     * 交互信息
     * @param interactionMsg
     */
    public void onInteractionMsg(InteractionMsg interactionMsg) {
    }
}
