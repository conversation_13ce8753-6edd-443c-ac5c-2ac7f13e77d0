package com.chief.engine.test;

import com.chief.engine.core.event.AddModelMsg;
import com.chief.engine.core.model.Actor;
import com.chief.engine.core.model.RootModel;
import com.chief.model.api.Interaction;
import com.chief.model.api.ModelObject;
import com.chief.model.api.ModelObjectHandler;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.chief.engine.core.constant.ID.ROOT_MODEL_ID;
import static java.util.concurrent.ForkJoinPool.defaultForkJoinWorkerThreadFactory;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TimeAdvanceTest {

    @Test
    public void testIdDuplicate() throws Exception {
        AtomicInteger count = new AtomicInteger();
        Thread.UncaughtExceptionHandler ueh = (thread, e)-> {
            count.incrementAndGet();
        };
        Actor.executor = new ForkJoinPool(Runtime.getRuntime().availableProcessors(),
                defaultForkJoinWorkerThreadFactory, ueh, false,
                0, 0x7fff, 1, null, 60_000L, TimeUnit.MILLISECONDS);

        RootModel rootModel = new RootModel(0, 1000L * 60);
        TestModelObject object = new TestModelObject();
        TestModelObjectComponent component = new TestModelObjectComponent();
        rootModel.send(new AddModelMsg(ROOT_MODEL_ID, "1", object));
        rootModel.send(new AddModelMsg("1", "1", component));

        Thread.sleep(1000);

        Assertions.assertTrue(count.get() != 0);


    }

    @Test
    public void testTimeAdvance() throws Exception {
        RootModel rootModel = new RootModel(0, 1000L * 60);
        TestModelObject object = new TestModelObject();
        TestModelObjectComponent component = new TestModelObjectComponent();
        rootModel.send(new AddModelMsg(ROOT_MODEL_ID, "1", object));
        rootModel.send(new AddModelMsg(ROOT_MODEL_ID, "2", component));

//        Thread.sleep(100);
        rootModel.start();

        rootModel.blockUtilStop();

        Assertions.assertEquals(object.a-1, 60);
        Assertions.assertEquals(component.a-1, 600);
    }

    public static class TestModelObject implements ModelObject {
        int a;

        @Override
        public Integer getFrameLength() {
            return 1000;
        }
    }
    public static class TestModelObjectHandler extends ModelObjectHandler<TestModelObject> {

        @Override
        public void update(long delta) {
            TestModelObject object = context.getModelObject();
            object.a++;
        }
    }
    public static class TestModelObjectComponent implements ModelObject {
        int a;

        @Override
        public Integer getFrameLength() {
            return 100;
        }
    }

    public static class TestModelObjectComponentHandler extends ModelObjectHandler<TestModelObjectComponent> {

        @Override
        public void update(long delta) {
            TestModelObjectComponent object = context.getModelObject();
            object.a++;
//            System.out.println(delta);
        }


    }


    public static class AskNumInteraction implements Interaction {
        CompletableFuture<Integer> future;

        public AskNumInteraction(CompletableFuture<Integer> future) {
            this.future = future;
        }
    }
}


