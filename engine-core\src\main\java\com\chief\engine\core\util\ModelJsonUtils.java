package com.chief.engine.core.util;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.SneakyThrows;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static org.springframework.util.ObjectUtils.isEmpty;

/**
 * 模型专用
 */
public class ModelJsonUtils {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        MAPPER.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
        MAPPER.setVisibility(PropertyAccessor.GETTER, JsonAutoDetect.Visibility.NONE);
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        MAPPER.registerModule(javaTimeModule);
    }

    public static ObjectMapper getObjectMapper() {
        return MAPPER;
    }

    @SneakyThrows
    public static String toJson(Object data) {
        if (data == null) {
            return null;
        }
        return MAPPER.writeValueAsString(data);
    }

    @SneakyThrows
    public static <T> T parse(String jsonData, Class<T> clazz) {
        if (!StringUtils.hasText(jsonData) || clazz == null) {
            return null;
        }
        return MAPPER.readValue(jsonData, clazz);
    }

    /**
     * 反序列化
     *
     * @param jsonData
     * @param clazz
     * @param <T>
     * @return
     */
    @SneakyThrows
    public static <T> T parse(byte[] jsonData, Class<T> clazz) {
        if (isEmpty(jsonData) || clazz == null) {
            return null;
        }
        return MAPPER.readValue(jsonData, clazz);
    }

    @SneakyThrows
    public static Map<?, ?> toMap(String string) {
        if (StringUtils.isEmpty(string)) {
            return null;
        }
        return MAPPER.readValue(string, Map.class);
    }

    @SneakyThrows
    public static List<?> toList(String string) {
        if (StringUtils.isEmpty(string)) {
            return null;
        }
        return MAPPER.readValue(string, List.class);
    }

    @SneakyThrows
    public static <T> T parse(String result, TypeReference<T> reference) {
        if (StringUtils.isEmpty(result) || reference == null) {
            return null;
        }
        return MAPPER.readValue(result, reference);
    }

    @SneakyThrows
    public static <T> T parse(byte[] result, TypeReference<T> reference) {
        if (result == null || reference == null) {
            return null;
        }
        return MAPPER.readValue(result, reference);
    }
}
