package com.chief.engine.core.util;

import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ExecutorGroup {

    Map<Integer, AtomicInteger> show = new ConcurrentHashMap<>();
    private int numThreads;
    private ExecutorService[] executors;
    private AtomicInteger index = new AtomicInteger(-1);

    public ExecutorGroup(int numThreads) {
        this.numThreads = numThreads;
        executors = new ExecutorService[numThreads];

        for (int i = 0; i < numThreads; i++) {
            executors[i] = new ThreadPoolExecutor(1, 1,
                    0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<Runnable>());
        }
    }


    public ExecutorService next() {
        int d = index.incrementAndGet() % numThreads;
        show.computeIfAbsent(d, k -> new AtomicInteger(0)).incrementAndGet();
        return executors[d];
    }

    public void show() {
        show.forEach((k, v) -> {
            System.out.println(k + ":" + v);
        });
    }
}
